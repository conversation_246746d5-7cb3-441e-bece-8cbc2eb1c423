export interface EntityType {
  id: string; // or number, confirm from actual API response
  name: string;
  slug: string;
  iconUrl?: string | null; // Added iconUrl, optional
  description?: string | null; // Added description, optional
}

export interface Category {
  id: string; // or number
  name: string;
  slug: string;
  // Add other relevant fields if any, e.g., description, parentId
}

export interface Tag {
  id: string; // or number
  name: string;
  slug: string;
  // Add other relevant fields if any
}

// Define specific detail interfaces if their structure is known and needed for the card
// For now, using a generic 'details' field
export interface ToolDetails {
  technical_level?: 'Beginner' | 'Intermediate' | 'Advanced' | 'Expert' | string; // Allow string for flexibility if enum not strict from backend
  key_features?: string[];
  integrations?: string[];
  use_cases?: string[];
  pricing_model?: 'Free' | 'Freemium' | 'Paid' | 'Subscription' | 'One-time Purchase' | string;
  api_available?: boolean;
  self_hosted_option?: boolean;
  // Add any other relevant fields for tools
  [key: string]: any; // Allow other dynamic fields if necessary
}

export interface CourseDetails {
  instructor_name?: string;
  duration_text?: string; // e.g., "10 hours", "3 Weeks"
  level?: 'Beginner' | 'Intermediate' | 'Advanced' | string;
  certificate_available?: boolean;
  prerequisites?: string[];
  learning_outcomes?: string[];
  language?: string;
  // Add any other relevant fields for courses
  [key: string]: any; // Allow other dynamic fields
}

// You can define other detail types here as needed:
export interface AgencyDetails {
  services_offered?: string[];
  portfolio_url?: string;
  team_size?: number | string; // e.g., "10-50" or a number
  specializations?: string[];
  contact_email?: string;
  region_served?: string;
  [key: string]: any;
}

export interface ContentCreatorDetails {
  platform?: 'YouTube' | 'TikTok' | 'Twitch' | 'Instagram' | 'Blog' | 'Podcast' | string;
  platform_url?: string;
  subscriber_count?: string; // e.g., "100K+", "1M"
  content_focus?: string[];
  collaboration_email?: string;
  sample_work_links?: string[];
  [key: string]: any;
}

export interface CommunityDetails {
  platform_name?: string; // e.g., "Discord", "Slack", "Facebook Group", "Forum"
  platform_url?: string;
  member_count?: string; // e.g., "10k+"
  main_topics?: string[];
  moderator_info?: string;
  entry_requirements?: string;
  [key: string]: any;
}

export interface NewsletterDetails {
  publication_schedule?: string; // e.g., "Weekly", "Bi-weekly", "Monthly"
  target_audience?: string;
  archive_url?: string;
  subscription_link?: string;
  author_name?: string;
  topics_covered?: string[];
  [key: string]: any;
}

export interface Entity {
  id: string; // or number
  name: string;
  description: string;
  websiteUrl: string;
  logoUrl: string | null;
  entityType: EntityType; // Or just string if API returns slug/name directly
  category: Category;    // Or just string
  tags: Tag[];
  averageRating: number;
  reviewCount: number;
  status: string; // e.g., 'ACTIVE', 'PENDING' - consider creating an enum if values are fixed
  createdAt: string; // ISO date string
  updatedAt: string; // ISO date string
  submittedByUserId?: string; // Or a minimal user profile object
  details?: ToolDetails | CourseDetails | AgencyDetails | ContentCreatorDetails | CommunityDetails | NewsletterDetails | Record<string, any>; 
}

export interface PaginationMeta {
  total: number;
  page: number;
  limit: number;
  totalItems: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
}

export interface PaginatedEntities {
  data: Entity[];
  meta: PaginationMeta;
}

// Interface for query parameters for fetching entities
export interface GetEntitiesParams {
  page?: number;
  limit?: number;
  searchTerm?: string;
  categoryIds?: string[]; // Ensure this is present
  tagIds?: string[];     // Ensure this is present
  entityTypeIds?: string[]; // Renamed from entityTypes to entityTypeIds
  sortBy?: string;       
  sortOrder?: 'asc' | 'desc';
  status?: string; 
} 