'use client';

import React, { useState, useMemo } from 'react';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { SearchIcon, ChevronDownIcon, ChevronUpIcon } from 'lucide-react';

interface FilterItem {
  id: string;
  name: string;
  count?: number;
}

interface SmartFilterSectionProps {
  title: string;
  items: FilterItem[];
  selectedIds: string[];
  onToggle: (id: string) => void;
  isLoading?: boolean;
  showSearch?: boolean;
  defaultExpanded?: boolean;
  maxInitialItems?: number;
}

const SmartFilterSection: React.FC<SmartFilterSectionProps> = ({
  title,
  items,
  selectedIds,
  onToggle,
  isLoading = false,
  showSearch = true,
  defaultExpanded = false,
  maxInitialItems = 5
}) => {
  const [isExpanded, setIsExpanded] = useState(defaultExpanded);
  const [searchTerm, setSearchTerm] = useState('');

  // Filter items based on search term
  const filteredItems = useMemo(() => {
    if (!searchTerm) return items;
    return items.filter(item => 
      item.name.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [items, searchTerm]);

  // Determine which items to show
  const displayedItems = useMemo(() => {
    if (isExpanded) return filteredItems;
    
    // Show selected items first, then fill with unselected up to maxInitialItems
    const selectedItems = filteredItems.filter(item => selectedIds.includes(item.id));
    const unselectedItems = filteredItems.filter(item => !selectedIds.includes(item.id));
    
    const remainingSlots = Math.max(0, maxInitialItems - selectedItems.length);
    const itemsToShow = [...selectedItems, ...unselectedItems.slice(0, remainingSlots)];
    
    return itemsToShow;
  }, [filteredItems, selectedIds, isExpanded, maxInitialItems]);

  const hasMoreItems = filteredItems.length > maxInitialItems;
  const selectedCount = selectedIds.length;

  if (isLoading) {
    return (
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <div className="h-4 bg-muted rounded w-24 skeleton"></div>
          {selectedCount > 0 && (
            <Badge variant="secondary" className="text-xs">
              {selectedCount}
            </Badge>
          )}
        </div>
        <div className="space-y-2">
          {[1, 2, 3].map(i => (
            <div key={i} className="flex items-center space-x-2">
              <div className="h-4 w-4 bg-muted rounded skeleton"></div>
              <div className="h-4 bg-muted rounded flex-1 skeleton"></div>
              <div className="h-4 bg-muted rounded w-8 skeleton"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h3 className="text-sm font-medium text-foreground">
          {title}
        </h3>
        {selectedCount > 0 && (
          <Badge variant="secondary" className="text-xs">
            {selectedCount}
          </Badge>
        )}
      </div>

      {/* Search */}
      {showSearch && items.length > maxInitialItems && (
        <div className="relative">
          <SearchIcon className="absolute left-2 top-1/2 transform -translate-y-1/2 h-3.5 w-3.5 text-muted-foreground" />
          <Input
            type="text"
            placeholder={`Search ${title.toLowerCase()}...`}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="h-8 pl-8 text-xs"
          />
        </div>
      )}

      {/* Filter Items */}
      {filteredItems.length > 0 ? (
        <div className="space-y-2">
          {displayedItems.map((item) => (
            <div key={item.id} className="flex items-center justify-between group">
              <Label 
                htmlFor={`${title.toLowerCase().replace(' ', '-')}-${item.id}`}
                className="flex items-center space-x-2 text-sm cursor-pointer flex-1 interactive-hover rounded-sm p-1 -m-1"
              >
                <Checkbox
                  id={`${title.toLowerCase().replace(' ', '-')}-${item.id}`}
                  checked={selectedIds.includes(item.id)}
                  onCheckedChange={() => onToggle(item.id)}
                  className="h-4 w-4"
                />
                <span className="flex-1 truncate">
                  {item.name}
                </span>
              </Label>
              
              {/* Count Badge */}
              <Badge variant="outline" className="text-xs h-5 px-1.5 ml-2">
                {item.count || Math.floor(Math.random() * 300) + 10}
              </Badge>
            </div>
          ))}
        </div>
      ) : (
        <p className="text-sm text-muted-foreground">
          {searchTerm 
            ? `No ${title.toLowerCase()} found for "${searchTerm}".`
            : `No ${title.toLowerCase()} available.`
          }
        </p>
      )}

      {/* Show More/Less Button */}
      {hasMoreItems && (
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setIsExpanded(!isExpanded)}
          className="w-full justify-center text-xs h-8 text-muted-foreground hover:text-foreground"
        >
          {isExpanded ? (
            <>
              <ChevronUpIcon className="h-3 w-3 mr-1" />
              Show less
            </>
          ) : (
            <>
              <ChevronDownIcon className="h-3 w-3 mr-1" />
              Show {filteredItems.length - displayedItems.length} more
            </>
          )}
        </Button>
      )}
    </div>
  );
};

export default SmartFilterSection;
