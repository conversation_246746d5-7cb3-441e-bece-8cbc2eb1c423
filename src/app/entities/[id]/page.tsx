'use client';

import React, { useEffect, useState, useCallback } from 'react';
import { useParams } from 'next/navigation';
import { getEntityById, getReviewsByEntityId, submitReview } from '@/services/api';
import { Entity, PaginationMeta } from '@/types/entity';
import { Review } from '@/types/review';
import DetailedResourceView from '@/components/resource/DetailedResourceView';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { ReviewFormData } from '@/components/resource/ReviewForm';

const REVIEWS_PAGE_SIZE = 5; // Number of reviews to fetch per page/load more click

export default function EntityDetailPage() {
  const params = useParams();
  const entityId = params?.id as string;

  const [entity, setEntity] = useState<Entity | null>(null);
  const [reviews, setReviews] = useState<Review[]>([]);
  const [reviewsPaginationMeta, setReviewsPaginationMeta] = useState<PaginationMeta | null>(null);
  const [currentReviewPage, setCurrentReviewPage] = useState(1);
  const [isLoadingEntity, setIsLoadingEntity] = useState(true);
  const [isLoadingReviews, setIsLoadingReviews] = useState(false); // Separate loading for reviews
  const [error, setError] = useState<string | null>(null);
  const [isSubmittingReview, setIsSubmittingReview] = useState(false);
  const [reviewSubmissionError, setReviewSubmissionError] = useState<string | null>(null);
  const [reviewSubmissionSuccess, setReviewSubmissionSuccess] = useState<string | null>(null);

  const { session } = useAuth();

  const fetchEntityData = useCallback(async () => {
    if (!entityId) {
      setError('Entity ID not found in URL.');
      setIsLoadingEntity(false);
      return;
    }
    setIsLoadingEntity(true);
    setError(null);
    try {
      const entityData = await getEntityById(entityId, session?.access_token);
      setEntity(entityData);
    } catch (err: unknown) {
      console.error(`Failed to fetch entity ${entityId}:`, err);
      const errorMessage = err instanceof Error ? err.message : 'An unexpected error occurred while fetching entity details.';
      setError(errorMessage);
    }
    setIsLoadingEntity(false);
  }, [entityId, session]);

  const fetchReviewsData = useCallback(async (page: number, loadMore = false) => {
    if (!entityId) return;
    setIsLoadingReviews(true);
    // setError(null); // Don't clear main error when loading more reviews
    try {
      const reviewsData = await getReviewsByEntityId(entityId, session?.access_token, page, REVIEWS_PAGE_SIZE);
      setReviews(prevReviews => loadMore ? [...prevReviews, ...reviewsData.data] : reviewsData.data);
      setReviewsPaginationMeta(reviewsData.meta);
      setCurrentReviewPage(reviewsData.meta.page || 1);
    } catch (err: unknown) {
      console.error(`Failed to fetch reviews for entity ${entityId}:`, err);
      // setError(err.message || 'An unexpected error occurred while fetching reviews.'); // Optionally set a specific review error
    }
    setIsLoadingReviews(false);
  }, [entityId, session]);

  useEffect(() => {
    fetchEntityData();
    fetchReviewsData(1); // Fetch initial page of reviews
  }, [fetchEntityData, fetchReviewsData]); // Removed entityId from deps as it's in fetchEntityData/fetchReviewsData

  const handleLoadMoreReviews = () => {
    if (reviewsPaginationMeta?.hasNextPage) {
      fetchReviewsData(currentReviewPage + 1, true);
    }
  };

  const handleReviewSubmit = async (data: ReviewFormData) => {
    if (!session?.access_token) {
      setReviewSubmissionError('You must be logged in to submit a review.');
      return;
    }
    if (!entityId) {
      setReviewSubmissionError('Entity ID is missing.');
      return;
    }

    setIsSubmittingReview(true);
    setReviewSubmissionError(null);
    setReviewSubmissionSuccess(null);

    try {
      const submissionPayload = {
        ...data,
        entityId: entityId,
      };
      // Ensure rating is a number if it comes as a string from the form
      submissionPayload.rating = Number(submissionPayload.rating);

      await submitReview(submissionPayload, session.access_token);
      setReviewSubmissionSuccess('Review submitted successfully for moderation!');
      // Optionally, re-fetch reviews or optimistically add the new review
      // For simplicity, we can just show a success message. A full refresh can be done by user or after a delay.
      // Or, better: reload the first page of reviews to see the new one if it's auto-approved or for consistency.
      fetchReviewsData(1, false); // Reload first page of reviews
      
    } catch (err: unknown) {
      console.error('Failed to submit review:', err);
      const errorMessage = err instanceof Error ? err.message : 'An unexpected error occurred while submitting your review.';
      setReviewSubmissionError(errorMessage);
    } finally {
      setIsSubmittingReview(false);
      // Clear success/error messages after a few seconds
      setTimeout(() => {
        setReviewSubmissionSuccess(null);
        setReviewSubmissionError(null);
      }, 5000);
    }
  };

  if (isLoadingEntity) {
    return (
      <div className="container mx-auto px-4 py-12 text-center">
        <p className="text-xl text-gray-600 dark:text-gray-300">Loading entity details...</p>
      </div>
    );
  }

  if (error && !entity) { // Show main error only if entity hasn't loaded
    return (
      <div className="container mx-auto px-4 py-12 text-center">
        <p className="text-xl text-red-500">Error: {error}</p>
        <Button onClick={fetchEntityData} className="mt-4">
          Try Again
        </Button>
      </div>
    );
  }

  if (!entity) {
    return (
      <div className="container mx-auto px-4 py-12 text-center">
        <p className="text-xl text-gray-600 dark:text-gray-300">Entity not found.</p>
      </div>
    );
  }

  return (
    <DetailedResourceView 
      entity={entity} 
      reviews={reviews} 
      onLoadMoreReviews={handleLoadMoreReviews}
      hasMoreReviews={reviewsPaginationMeta?.hasNextPage || false}
      isLoadingReviews={isLoadingReviews}
      reviewsTotalCount={reviewsPaginationMeta?.total}
      onSubmitReview={handleReviewSubmit}
      isSubmittingReview={isSubmittingReview}
      reviewSubmissionError={reviewSubmissionError}
      reviewSubmissionSuccess={reviewSubmissionSuccess}
    />
  );
} 