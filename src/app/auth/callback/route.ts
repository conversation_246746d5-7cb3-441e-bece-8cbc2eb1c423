// src/app/auth/callback/route.ts
import { createServerClient, type CookieOptions } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { NextResponse, type NextRequest } from 'next/server';
// import type { Database } from '@/lib/database.types'; // If you have a typed Supabase client

export const dynamic = 'force-dynamic'; // Ensures this route is always dynamic

export async function GET(request: NextRequest) {
  const { searchParams, origin } = new URL(request.url);
  const code = searchParams.get('code');
  // if "next" is in param, use it as the redirect URL
  const next = searchParams.get('next') ?? '/'; // Default to homepage

  if (code) {
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookies().get(name)?.value;
          },
          set(name: string, value: string, options: CookieOptions) {
            try {
              cookies().set({ name, value, ...options });
            } catch (error) {
              console.warn(`[AuthCallback] Cookie set failed for ${name}:`, error);
            }
          },
          remove(name: string, options: CookieOptions) {
            try {
              cookies().set({ name, value: '', ...options, maxAge: 0 });
            } catch (error) {
              console.warn(`[AuthCallback] Cookie remove failed for ${name}:`, error);
            }
          },
        },
      }
    );
    const { error } = await supabase.auth.exchangeCodeForSession(code);
    if (!error) {
      console.log('[AuthCallback] Successfully exchanged code for session. Redirecting to:', `${origin}${next}`);
      return NextResponse.redirect(`${origin}${next}`);
    }
    console.error('[AuthCallback] Error exchanging code for session:', error);
  } else {
    console.error('[AuthCallback] No code found in search params.');
  }

  // Redirect to an error page or home if code is missing or exchange fails
  console.log('[AuthCallback] Redirecting to auth-code-error.');
  return NextResponse.redirect(`${origin}/auth/auth-code-error`); // Create this page
} 