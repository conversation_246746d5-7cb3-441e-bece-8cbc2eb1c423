import { type EmailOtpType } from '@supabase/supabase-js';
import { type NextRequest, NextResponse } from 'next/server';
import { createServerClient, type CookieOptions } from '@supabase/ssr';
import { cookies } from 'next/headers';

export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const token_hash = searchParams.get('token_hash');
  const type = searchParams.get('type') as EmailOtpType | null;
  const next = searchParams.get('next') ?? '/'; // Default redirect to home

  console.log('[AuthConfirm] Received request. URL:', request.url);
  console.log('[AuthConfirm] token_hash:', token_hash, 'type:', type, 'next:', next);

  if (token_hash && type) {
    const cookieStore = cookies();
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
          set(name: string, value: string, options: CookieOptions) {
            // If the cookie is deleted, use `delete` as before
            // if (options.maxAge === 0) {
            //   cookieStore.delete(name);
            // } else {
            //   cookieStore.set({ name, value, ...options });
            // }
            // Using try-catch for now due to potential ReadonlyRequestCookies issue
            try {
              cookieStore.set({ name, value, ...options });
            } catch (error) {
              // Fallback for environments where `set` might not be available directly on ReadonlyRequestCookies
              // This is a workaround and might indicate a deeper issue if it's triggered often.
              console.error('[AuthConfirm] Error setting cookie directly, attempting response cookies:', error);
              // This part is tricky for GET handlers if we also need to redirect.
              // The primary goal here is that createServerClient needs these cookie fns to work.
            }
          },
          remove(name: string, options: CookieOptions) {
            // cookieStore.delete({ name, ...options });
            // Using try-catch for now
            try {
              cookieStore.set({ name, value: '', ...options });
            } catch (error) {
              console.error('[AuthConfirm] Error removing cookie directly, attempting response cookies:', error);
            }
          },
        },
      }
    );

    console.log('[AuthConfirm] Verifying OTP...');
    const { error, data } = await supabase.auth.verifyOtp({
      type,
      token_hash,
    });

    if (!error) {
      console.log('[AuthConfirm] OTP verified successfully. Data:', data);
      // It's important that the session is set in the cookie by the verifyOtp call
      // when using the server client with the cookie helpers.
      // Then redirect the user.
      console.log('[AuthConfirm] Redirecting to:', next);
      return NextResponse.redirect(new URL(next, request.url).toString());
    } else {
      console.error('[AuthConfirm] Error verifying OTP:', error);
      // Redirect to an error page or show an error message
      const errorPage = new URL('/auth/auth-error', request.url);
      errorPage.searchParams.set('error', error.message);
      return NextResponse.redirect(errorPage.toString());
    }
  }

  // If token_hash or type is missing, redirect to an error page
  console.warn('[AuthConfirm] Missing token_hash or type. Redirecting to error page.');
  const missingParamsErrorPage = new URL('/auth/auth-error', request.url);
  missingParamsErrorPage.searchParams.set('error', 'Invalid confirmation link: Missing token or type.');
  return NextResponse.redirect(missingParamsErrorPage.toString());
} 