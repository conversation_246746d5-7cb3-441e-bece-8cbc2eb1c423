'use client';

import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect } from 'react';

export default function AuthCodeErrorPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const error = searchParams.get('error');
  const message = searchParams.get('message');

  useEffect(() => {
    // Optional: redirect to login after a few seconds
    // const timer = setTimeout(() => {
    //   router.push('/login');
    // }, 7000); // 7 seconds
    // return () => clearTimeout(timer);
  }, [router]);

  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-gray-100 p-4">
      <div className="bg-white shadow-xl rounded-lg p-8 md:p-12 max-w-md w-full text-center">
        <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 text-red-500 mx-auto mb-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="2">
          <path strokeLinecap="round" strokeLinejoin="round" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        <h1 className="text-3xl font-bold text-gray-800 mb-4">Authentication Error</h1>
        <p className="text-gray-600 mb-2">
          We encountered a problem trying to sign you in.
        </p>
        {error && (
          <p className="text-sm text-gray-500 mt-1">
            <span className="font-semibold">Error Code:</span> {error}
          </p>
        )}
        {message && (
          <p className="text-sm text-gray-500 bg-red-50 p-3 rounded-md mt-3">
            {decodeURIComponent(message)}
          </p>
        )}
        <div className="mt-8">
          <Link href="/login">
            <span className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-150 ease-in-out cursor-pointer">
              Return to Login
            </span>
          </Link>
        </div>
        <p className="text-xs text-gray-400 mt-8">
          If the problem persists, please contact support.
        </p>
      </div>
    </div>
  );
} 